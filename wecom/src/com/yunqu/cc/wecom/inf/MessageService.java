package com.yunqu.cc.wecom.inf;

import java.sql.SQLException;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;
import org.apache.log4j.Logger;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.EasyCache;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.kit.RandomKit;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.servlet.model.GWConstants;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.DateUtil;
import com.yunqu.cc.wecom.base.CommonLogger;
import com.yunqu.cc.wecom.base.Constants;
import com.yunqu.cc.wecom.utils.TimeUtil;

public class MessageService extends IService{

	private Logger logger = CommonLogger.getLogger("session");
	private EasyQuery easyQuery =  EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_YW);
	private EasyQuery ycbusiQuery =  EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_YCBUSI);
	private EasyQuery ycuserQuery =  EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_YCUSER);
	public static EasyCache cache = CacheManager.getMemcache();
	
	@Override
	public JSONObject invoke(JSONObject resqJson) throws ServiceException {
		String command = resqJson.getString("command");
		if("organizeWecomChatDetail".equals(command)){
			JSONObject obj = new JSONObject();
			handleSession();
			return obj;
		}else{
			JSONObject result = new JSONObject();
			result.put("respCode", GWConstants.RET_CODE_COMMAND_ERROR);
			result.put("respDesc", "不存在的command,请检查！");
			return result;
		}
	}
	
	/**
	 * 会话汇总
	 */
	public void handleSession() {
		String currentTime = DateUtil.getCurrentDateStr("yyyy-MM-dd");
		String beginTime = DateUtil.addDay("yyyy-MM-dd", currentTime, -2) + " 18:00:00";
		String endTime = DateUtil.addDay("yyyy-MM-dd", currentTime, -1) + " 18:00:00";
		EasySQL sql = new EasySQL("SELECT * FROM C_NO_WECOM_RECORD WHERE 1=1");
		sql.append(beginTime,"AND MSG_TIME >= ?");
		sql.append(endTime,"AND MSG_TIME < ?");
		sql.append("ORDER BY SENDER,RECEIVER,MSG_TIME DESC");
		logger.info("查询企微聊天记录数据sql：" + sql.getSQL() + ",参数" + JSON.toJSONString(sql.getParams()));
		//渠道信息
		JSONObject wecomChannelInfo = getWeComChannelInfo();
		//登录坐席
		JSONObject wecomLoginInfo = getWeComLoginInfo();
		try {
			JSONObject sessionList = new JSONObject();
			easyQuery.setMaxRow(999999);
			List<EasyRow> list = easyQuery.queryForList(sql.getSQL(), sql.getParams());
			logger.info("查询企微聊天记录数据数量：" + list.size());

			for(EasyRow r :list) {
				String sender = r.getColumnValue("SENDER");
				String receiver = r.getColumnValue("RECEIVER");
				String msgTime = r.getColumnValue("MSG_TIME");
				String key = sender + "@" + receiver;
				String senderType = "2";//发送者:1 客户 2 坐席 3 机器人 4 系统
				String externalUserId = receiver;//外部联系人id
				// 确保 key 一定是 坐席@客户
				if((sender.startsWith("wo")||sender.startsWith("wm"))&&sender.length()>20) {
					senderType = "1";
					externalUserId = sender;
					key = receiver + "@" + sender;
				}
				//确认当前聊天记录是哪个坐席
				// 第一层判断：根据埋点记录获取
				JSONObject info = checkAgentInfo(key, wecomLoginInfo, msgTime);
				//第二层判断：直接拿坐席信息 在 C_NO_WECOM_LOGIN_LOG 查询当天是否有记录信息
				if(info.isEmpty()) {
					logger.info("sender:" + sender + ",receiver:" + receiver);
					String agentMes = "";
					if("1".equals(senderType)) {  // sender为客户 用receiver
						agentMes = receiver;
					} else {  // sender为坐席 用sender
						agentMes = sender;
					}
					info = cache.get("SYN_"+agentMes);
					if (info == null || info.isEmpty()){
						// 拿到key的前面部分@ 从数据库中拿到对应的坐席信息 select * from C_NO_WECOM_LOGIN_LOG where WX_AGENT_USERID = ?
						EasySQL agentSql = new EasySQL("select * from C_NO_WECOM_LOGIN_LOG where 1=1");
						agentSql.append(beginTime,"and create_time >= ?");
						agentSql.append(endTime,"and create_time < ?");
						agentSql.append(agentMes,"AND WX_AGENT_USERID = ? ");
						logger.info(key + "在坐席操作记录表中没有记录,采用第二层判断获取坐席信息,查询坐席登录记录sql：" + agentSql.getSQL() + ",参数" + JSON.toJSONString(agentSql.getParams()));
						List<JSONObject> agentList = easyQuery.queryForList(agentSql.getSQL(), agentSql.getParams(), new JSONMapperImpl());
						if(agentList == null || agentList.isEmpty() || agentList.size()==0) {
							//第二层拿不到在跳过
							logger.info(key + "在坐席操作记录表中当天也没有记录,第二层判断拿不到,决定跳过");
							continue;
						}
						info = agentList.get(0);
						// 保底措施 WX_EXT_NAME更新为用户id
						if("1".equals(senderType)) {  // sender为客户 用sender
							info.put("WX_EXT_NAME", sender);
						} else { // sender为坐席 用receiver
							info.put("WX_EXT_NAME", receiver);
						}
						//缓存十分钟
						cache.put("SYN_"+agentMes, info, 10 * 60);
					} else {
						// 缓存命中
						logger.info( agentMes + "缓存命中,使用缓存");
					}
				}
				JSONArray arr = sessionList.getJSONArray(key);
				if(arr==null) {
					arr = new JSONArray();
				}
				JSONObject obj = new JSONObject();
				obj.put("CHAT_ID",RandomKit.uniqueStr());
				obj.put("ENT_ID",1000);
				obj.put("DATE_ID",TimeUtil.getDateFormat(msgTime, TimeUtil.DATE_TIME_STR, "yyyyMMdd"));
				obj.put("MSG_TIME",msgTime);
				obj.put("MSG_TIMESTAMP",TimeUtil.getDateTimestamp(msgTime, TimeUtil.DATE_TIME_STR));
				obj.put("SENDER",senderType);
				obj.put("CUST_SESSION_ID",externalUserId);
				obj.put("CUST_NAME",info.getString("WX_EXT_NAME"));
				obj.put("AGENT_ID",info.getString("AGENT_NO")+"@1000");
				obj.put("AGENT_NO",info.getString("AGENT_NO"));
				obj.put("AGENT_ACC",info.getString("AGENT_ACC"));
				obj.put("AGENT_NAME",info.getString("AGENT_NAME"));
				obj.put("AGENT_DEPT",info.getString("AGENT_DEPT"));
				obj.put("AGENT_AREA",info.getString("AGENT_AREA"));
				obj.put("MSG_TYPE",r.getColumnValue("MSG_TYPE"));
				obj.put("MSG_CONTENT",r.getColumnValue("MSG_CONTENT"));
				arr.add(obj);
				sessionList.put(key, arr);
			}
			for(String k:sessionList.keySet()) {
				logger.info("同步企微会话记录开始，key====>:"+k);
				generateSession(sessionList.getJSONArray(k),wecomChannelInfo);
			}
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "处理会话记录error:" + e.getMessage(),e);
		}
	}
	/**
	 * 生成会话记录
	 */
	public void generateSession(JSONArray arr, JSONObject channel) {
		try {
			JSONArray chatRecordList = arr;
			String chatSessionId = RandomKit.uniqueStr();//生成本次会话流水ID
			JSONObject json = chatRecordList.getJSONObject(0);
			String beginTime = "";
			String endTime = "";
			int serverTime = 0;
			for(int j=0;j<chatRecordList.size();j++) {
				JSONObject chat = chatRecordList.getJSONObject(j);
				//存储企微聊天记录信息
				EasyRecord chatRecord = new EasyRecord("CC_MEDIA_CHAT_RECORD","CHAT_ID");
				chatRecord.set("CHAT_ID", RandomKit.uniqueStr());
				chatRecord.set("ENT_ID", "1000");
				chatRecord.set("DATE_ID", TimeUtil.getDateFormatByTimestamp(chat.getLongValue("MSG_TIMESTAMP"), "yyyyMMdd"));
				chatRecord.set("MSG_TIME", chat.getString("MSG_TIME"));
				chatRecord.set("CHAT_SESSION_ID", chatSessionId);
				chatRecord.set("CUST_SESSION_ID", chat.getString("CUST_SESSION_ID"));
				chatRecord.set("SENDER", chat.getString("SENDER"));
				if("2".equals(chat.getString("SENDER"))) {
					chatRecord.set("AGENT_ID", chat.getString("AGENT_ID"));
				}
				chatRecord.set("MSG_TYPE", chat.getString("MSG_TYPE"));
				chatRecord.set("MSG_CONTENT", chat.getString("MSG_CONTENT"));
				chatRecord.set("MSG_TIMESTAMP", chat.getString("MSG_TIMESTAMP"));
				chatRecord.set("READ_STATE", 1);
				ycbusiQuery.save(chatRecord);
				
				beginTime = "".equals(beginTime) ? chat.getString("MSG_TIME"):beginTime;
				endTime = "".equals(endTime) ? chat.getString("MSG_TIME"):endTime;
				//找出最早时间
				if(DateUtil.compareDate(beginTime, chat.getString("MSG_TIME"), "yyyy-MM-dd HH:mm:ss")==1) {
					beginTime = chat.getString("MSG_TIME");
				}
				//找出最晚时间
				if(DateUtil.compareDate(endTime, chat.getString("MSG_TIME"), "yyyy-MM-dd HH:mm:ss")==-1) {
					endTime = chat.getString("MSG_TIME");
				}
			}
			serverTime = DateUtil.bwSeconds(beginTime,endTime, "yyyy-MM-dd HH:mm:ss");
			//存储坐席企微会话记录信息
			EasyRecord record = new EasyRecord("CC_MEDIA_RECORD");
			record.set("SERIAL_ID", chatSessionId);
			record.set("SESSION_ID", json.getString("CUST_SESSION_ID"));
			record.set("MONTH_ID", TimeUtil.getDateFormatByTimestamp(json.getLongValue("MSG_TIMESTAMP"), "yyyyMM"));
			record.set("DATE_ID", TimeUtil.getDateFormatByTimestamp(json.getLongValue("MSG_TIMESTAMP"), "yyyyMMdd"));
			record.set("ENT_ID", "1000");
			record.set("ENT_NAME", "midea");
			record.set("CHANNEL_ID", channel.getString("channelId"));
			record.set("CHANNEL_TYPE", channel.getString("channelType"));
			record.set("CHANNEL_KEY", channel.getString("channelKey"));
			record.set("CHANNEL_NAME", channel.getString("channelName"));
			record.set("BEGIN_TIME", beginTime);
			record.set("END_TIME", endTime);
			record.set("SERVER_TIME", serverTime);
			record.set("CUST_CODE", json.getString("CUST_SESSION_ID"));
			record.set("CUST_NAME", json.getString("CUST_NAME"));
			record.set("AGENT_ID", json.getString("AGENT_ID"));
			record.set("AGENT_NO", json.getString("AGENT_NO"));
			record.set("AGENT_NAME", json.getString("AGENT_NAME"));
			record.set("AGENT_ACC", json.getString("AGENT_ACC"));
			record.set("AGENT_DEPT", json.getString("AGENT_DEPT"));
			record.set("OP_AREA_CODE", json.getString("AGENT_AREA"));
			record.set("EP_CODE", "001");
			record.set("CLEAR_CAUSE", 3);//系统挂断
			record.set("CREATE_CAUSE", 1);//呼入
			record.set("SERVER_STATE", 3);//服务结束
			record.set("VIDEO_FLAG", 0);//无视频
			record.set("QC_STATE", 0);//未质检
			record.set("STATUS", "09");//未质检
			ycbusiQuery.save(record);
			logger.info("同步企微会话记录成功====>");
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "ERROR:" + e.getMessage(),e);
		}
	}


	public JSONObject getWeComLoginInfo() {
		String currentTime = DateUtil.getCurrentDateStr("yyyy-MM-dd");
		String beginTime = DateUtil.addDay("yyyy-MM-dd", currentTime, -2) + " 18:00:00";
		String endTime = DateUtil.addDay("yyyy-MM-dd", currentTime, -1) + " 18:00:00";
		EasySQL sql = new EasySQL("select * from C_NO_WECOM_LOGIN_LOG where 1=1");
		sql.append(beginTime,"and create_time >= ?");
		sql.append(endTime,"and create_time < ?");
		logger.info("查询坐席登录记录sql：" + sql.getSQL() + "，参数：" + JSON.toJSONString(sql.getParams()));
		List<JSONObject> list = new ArrayList<JSONObject>();
		try {
			easyQuery.setMaxRow(99999);
			list = easyQuery.queryForList(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
		} catch (SQLException e) {
			logger.error("error:" + e.getMessage(),e);
		}
		JSONObject data = new JSONObject();
		for(JSONObject obj:list) {
			//坐席账号+成员id+外部联系人id组成key
			String key = obj.getString("AGENT_ACC") + obj.getString("WX_AGENT_USERID") + "@" + obj.getString("WX_EXT_USERID");
			data.put(key, obj);
		}
		return data;
	}
	
	/**
	 * 确认当前聊天记录是哪个坐席
	 * @param key
	 * @param data
	 * @return
	 */
	public JSONObject checkAgentInfo(String key, JSONObject data, String msgTime) {
		JSONArray arr = new JSONArray();
		for(String k:data.keySet()) {
			if(k.indexOf(key)!=-1) {
				arr.add(data.get(k));
			}
		}
		if(arr.size()==0) {
			return new JSONObject();
		}else if(arr.size()==1) {
			return arr.getJSONObject(0);
		}else {
			/**
			 * 当同一个用户存在多个坐席时
			 * 1.消息在多个坐席登录时间之前 ----- ?
			 * 2.第一个之后第二个之前算第一个...
			 * 3.第二个之后第三个之前算第二个...
			 */
			JSONObject respData = new JSONObject();
			for(int i=0;i<arr.size();i++) {
				JSONObject obj = arr.getJSONObject(i);
				//登录时间<消息时间,要找出离消息时间最近的登录时间
				if(DateUtil.compareDate(obj.getString("CREATE_TIME"), msgTime, "yyyy-MM-dd HH:mm:ss")==-1) {
					if(respData.isEmpty()) {
						respData = obj;
						continue;
					}
					if(DateUtil.bwSeconds(obj.getString("CREATE_TIME"),respData.getString("CREATE_TIME"), "yyyy-MM-dd HH:mm:ss") <= 0) {
						respData = obj;
					}
				}
			}
			return respData;
		}
	}

	
	/**
	 * 获取企微侧边栏渠道信息
	 * @return
	 */
	public JSONObject getWeComChannelInfo() {
		JSONObject result = new JSONObject();
		EasySQL sql = new EasySQL("SELECT * FROM CC_CHANNEL WHERE 1=1");
		sql.append(Constants.WECOM_CHANNEL_KEY,"AND CHANNEL_KEY = ?");
		try {
			EasyRow row = ycuserQuery.queryForRow(sql.getSQL(), sql.getParams());
			result.put("channelId", row.getColumnValue("CHANNEL_ID"));
			result.put("channelKey", row.getColumnValue("CHANNEL_KEY"));
			result.put("channelName", row.getColumnValue("CHANNEL_NAME"));
			result.put("channelType", row.getColumnValue("CHANNEL_TYPE"));
			logger.info("查询渠道信息：" + result);
		} catch (SQLException e) {
			logger.error("error:" + e.getMessage(),e);
		}
		return result;
	}

	
	/**
	 * 会话汇总
	 */
//	public JSONArray handleSession() {
//		String currentTime = DateUtil.getCurrentDateStr("yyyy-MM-dd");
//		String beginTime = DateUtil.addDay("yyyy-MM-dd", currentTime, -2) + " 18:00:00";
//		String endTime = DateUtil.addDay("yyyy-MM-dd", currentTime, -1) + " 18:00:00";
//		//获取userId
//		//JSONArray userIdList = getUserIdList();
//		EasySQL sql = new EasySQL("SELECT * FROM C_NO_WECOM_RECORD WHERE 1=1");
////		sql.append(beginTime,"AND MSG_TIME >= ?");
////		sql.append(endTime,"AND MSG_TIME < ?");
//		sql.append("ORDER BY SENDER,RECEIVER,MSG_TIME DESC");
//		//渠道信息
//		JSONObject wecomChannelInfo = getWeComChannelInfo();
//		//
//		JSONObject wecomLoginInfo = getWeComLoginInfo();
//		try {
//			JSONObject sessionList = new JSONObject();
//			List<EasyRow> list = QueryFactory.getQuery().queryForList(sql.getSQL(), sql.getParams());
//			for(EasyRow r:list) {
//				String sender = r.getColumnValue("SENDER");
//				String receiver = r.getColumnValue("RECEIVER");
//				String key = sender + "@" + receiver;
//				String senderType = "2";//发送者1 客户 2 坐席 3 机器人 4 系统
//				String externalUserId = receiver;//外部联系人id
//				String agentUserId = sender;
//				if(sender.startsWith("wo")||sender.startsWith("wm")) {
//					senderType = "1";
//					externalUserId = sender;
//					agentUserId = "";
//				}
//				JSONArray arr = sessionList.getJSONArray(key);
//				if(arr==null) {
//					arr = new JSONArray();
//				}
//				JSONObject obj = new JSONObject();
//				obj.put("CHAT_ID",RandomKit.uniqueStr());
//				obj.put("ENT_ID",1000);
//				obj.put("DATE_ID",TimeUtil.getDateFormat(r.getColumnValue("MSG_TIME"), TimeUtil.DATE_TIME_STR, "yyyyMMdd"));
//				obj.put("MSG_TIME",r.getColumnValue("MSG_TIME"));
////				obj.put("CHAT_SESSION_ID","");
//				obj.put("SENDER",senderType);
//				obj.put("CUST_SESSION_ID",externalUserId);
////				obj.put("AGENT_ID",r.getColumnValue("AGENT_ID"));
////				obj.put("AGENT_ACC",r.getColumnValue("AGENT_ACC"));
////				obj.put("AGENT_NAME",r.getColumnValue("AGENT_NAME"));
//				obj.put("MSG_TYPE",r.getColumnValue("MSG_TYPE"));
//				obj.put("MSG_CONTENT",r.getColumnValue("MSG_CONTENT"));
//				arr.add(obj);
//				sessionList.put(key, arr.toJSONString());
//			}
//			JSONArray newa = new JSONArray();
//			while(sessionList.keySet().size()!=0) {
//				String k1 = "";
//				String k2 = "";
//				JSONArray array1 = new JSONArray();
//				for(String firstKey:sessionList.keySet()) {
//					String[] l = firstKey.split("@");
//					String sender = l[0];
//					String receiver = l[1];
//					
//					String secondKey = receiver + "@" + sender;
//					
//					array1 = sessionList.getJSONArray(firstKey);
//					JSONArray array2 = sessionList.getJSONArray(secondKey);
//					/**
//					 * a@b和b@a视为同一通会话
//					 */
//					if(array1!=null&&array2!=null) {
//						array1.addAll(array2);
//					}
//					k1 = firstKey;
//					k2 = secondKey;
//					break;
//				}
//				//删除同一通会话
//				sessionList.remove(k1);
//				sessionList.remove(k2);
//				//生成会话
//				//generateSession(array1,wecomChannelInfo);
//				newa.add(array1);
//			}
//			return newa;
//		} catch (Exception e) {
//			logger.error(CommonUtil.getClassNameAndMethod(this) + "处理会话记录error:" + e.getMessage(),e);
//		}
//		return null;
//	}

	public static void main(String[] args) {
		String time = "2023-11-01";
		System.out.println("time:" + time);
		String beginTime = time + " 18:00:00";
		String endTime = DateUtil.addDay("yyyy-MM-dd", time, 1) + " 18:00:00";
		System.out.println("beginTime:"+beginTime);
		System.out.println("endTime:"+endTime);
		System.out.println(DateUtil.bwSeconds("2022-10-13 19:00:00","2022-10-13 20:00:00", "yyyy-MM-dd HH:mm:ss"));
		LocalTime currentTime = LocalTime.now();
		int hour = currentTime.getHour();
		System.out.println("hour:" + hour);
		String logger = "wechatDetail_" + hour;
		System.out.println("日志名称：" + logger);
		String msgTimestamp = "1700471017484";
		String msgTime = DateUtil.getDateStrByTimeStamp(Long.valueOf(msgTimestamp));
		System.out.println("msgTime:"+msgTime);


		String chat_id = RandomKit.uniqueStr();
		String dateid = "20231030";
		String chatSessionId = RandomKit.uniqueStr();//生成本次会话流水ID
		String agentName = "郭佳乐";
		System.out.println("chat_id:" + chat_id);
		System.out.println("dateid:" + dateid);
		System.out.println("chatSessionId:" + chatSessionId);
		System.out.println("agentName:" + agentName);
		System.out.println("你有一条群工单稽查通知");

	}
	
}